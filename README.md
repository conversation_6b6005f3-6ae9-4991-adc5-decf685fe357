# Rent Collection Toolkit

A modern web application for UK council and housing association rent collection agents, built with SvelteKit and designed for efficiency and ease of use.

## 🚀 Project Status

**Frontend Implementation: COMPLETE ✅**

All major frontend features have been successfully implemented according to the project plan:

- ✅ **Rent Calculator Tab** - Complete with all 13 calculations
- ✅ **Arrangement Planner Tab** - Complete with weekly projections and payment planning
- ✅ **Account Charges Tab** - Complete with UC/HB entitlement calculations
- ✅ **Dual Instance Support** - Inbound/Outbound instance management
- ✅ **Session Management** - Session log and case log functionality
- ✅ **Responsive Design** - Mobile-friendly interface
- ✅ **Authentication System** - Mock login with role-based access

## 🎯 Key Features

### Core Functionality
- **Three Main Tabs**: Rent Calculator, Arrangement Planner, Account Charges
- **Dual Instance Support**: Switch between Inbound and Outbound call types
- **Real-time Calculations**: All 28 calculations implemented as per specification
- **Data Synchronization**: Automatic data sharing between tabs
- **Notes Management**: Session notes with clipboard integration

### User Interface
- **Modern Design**: Clean, professional interface with color-coded instances
- **Responsive Layout**: Works on desktop, tablet, and mobile devices
- **Accessibility**: Keyboard navigation and screen reader friendly
- **Visual Feedback**: Color-coded shortfalls, surpluses, and thresholds

### Data Management
- **Static Data**: Currently uses mock data (easily replaceable with API calls)
- **Session Persistence**: Data maintained during user session
- **Export Functionality**: CSV export for case logs
- **Search & Filter**: Advanced filtering in case log view

## 🛠️ Technology Stack

- **Frontend**: SvelteKit (TypeScript)
- **Styling**: Custom CSS with CSS variables
- **State Management**: Svelte stores
- **Build Tool**: Vite
- **Package Manager**: npm

## 📋 Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- npm (v9 or higher)

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd rent-collection-toolkit

# Install dependencies
npm install

# Start development server
npm run dev

# Open browser to http://localhost:5173
```

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run check        # Run TypeScript checks
npm run check:watch  # Watch mode for TypeScript checks
```

## 🎮 Usage Guide

### Getting Started
1. **Login**: Use any email/password combination (mock authentication)
2. **Select Instance**: Choose between Inbound or Outbound call types
3. **Enter Data**: Start with the Rent Calculator tab
4. **Navigate Tabs**: Data automatically syncs between tabs
5. **Add Notes**: Use the notes section to record interactions

### Rent Calculator Tab
- Enter tenant reference and financial details
- View real-time calculations for shortfalls/surpluses
- Copy reference or rent information to clipboard
- All calculations update automatically as you type

### Arrangement Planner Tab
- Data automatically synced from Rent Calculator
- Plan payment arrangements with weekly projections
- View debt clearance timeframes
- Highlight critical arrears thresholds (4 and 8 weeks)

### Account Charges Tab
- Calculate UC and HB entitlements
- Break down service charges
- View shortfall analysis
- Support for under-occupation scenarios

### Session Management
- **Session Log**: View all notes from current session
- **Case Log**: Access persistent note history with search/filter
- **Export**: Download case logs as CSV files

## 🧮 Calculations Implemented

The application implements all 28 calculations as specified:

**Rent Calculator (1-13):**
- Monthly/weekly rent conversions
- Payment totals and shortfalls
- Tenant/benefits contributions
- Arrears clearance dates
- Refund calculations

**Arrangement Planner (14-17):**
- Months to clear debt
- Weekly balance projections
- Arrears after payments
- Required payment amounts

**Account Charges (18-28):**
- Gross monthly rent
- Service charge breakdowns
- UC/HB entitlements (full, low, high under-occupation)
- Shortfall calculations

## 🎨 Design System

### Color Coding
- **Inbound Calls**: Blue theme (`#3B82F6`)
- **Outbound Calls**: Green theme (`#10B981`)
- **Positive Values**: Green (surplus/credit)
- **Negative Values**: Red (shortfall/debt)
- **Thresholds**: Orange/Red for arrears levels

### Responsive Breakpoints
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: < 768px

## 🔧 Configuration

### Environment Variables
Currently using static data. When integrating with backend:

```env
VITE_API_BASE_URL=https://api.example.com
VITE_AUTH_ENDPOINT=/auth
VITE_TENANT_ENDPOINT=/tenants
```

### CSS Variables
Customize the design system by modifying CSS variables in `src/app.css`:

```css
:root {
  --color-primary: #3B82F6;
  --color-inbound: #3B82F6;
  --color-outbound: #10B981;
  /* ... more variables */
}
```

## 🚧 Next Steps (Backend Integration)

The frontend is ready for backend integration. Key integration points:

1. **Authentication API**: Replace mock login with real JWT authentication
2. **Tenant Management**: Connect to tenant database
3. **Data Persistence**: Replace static data with API calls
4. **Case Log Storage**: Implement persistent note storage
5. **Multi-tenancy**: Add organization-based data isolation

### API Endpoints Needed
```
POST /api/auth/login
GET  /api/tenants
POST /api/tenants
GET  /api/casedata/{tenantId}
POST /api/casedata
POST /api/notes
GET  /api/notes/{tenantId}
```

## 📁 Project Structure

```
src/
├── lib/
│   ├── components/          # Svelte components
│   │   ├── RentCalculator.svelte
│   │   ├── ArrangementPlanner.svelte
│   │   ├── AccountCharges.svelte
│   │   ├── SessionLog.svelte
│   │   ├── CaseLog.svelte
│   │   ├── NotesSection.svelte
│   │   └── LoginForm.svelte
│   └── stores/              # State management
│       └── index.ts
├── routes/                  # SvelteKit routes
│   ├── +layout.svelte
│   └── +page.svelte
├── app.css                  # Global styles
└── app.html                 # HTML template
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] All calculations produce correct results
- [ ] Data syncs between tabs correctly
- [ ] Instance switching maintains separate data
- [ ] Notes save to session log
- [ ] Case log filtering works
- [ ] Responsive design on all devices
- [ ] Accessibility features function

### Future Testing Implementation
- Unit tests for calculation functions
- Component testing with Vitest
- E2E testing with Playwright
- API integration tests

## 📖 Documentation

- **Project Plan**: See `docs/Project-Plan.md` for detailed implementation plan
- **API Documentation**: To be created during backend development
- **User Guide**: To be created for end users

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software for UK council and housing association use.

## 🆘 Support

For technical support or questions about the implementation, please refer to the project documentation or contact the development team.

---

**Built with ❤️ for UK housing professionals**
