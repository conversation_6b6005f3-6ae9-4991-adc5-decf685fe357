# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The Rent Collection Toolkit is a modern SvelteKit application for UK council and housing association rent collection agents. It implements a dual-instance system supporting both inbound and outbound call management with comprehensive rent calculations.

## Development Commands

```bash
# Development
npm run dev                 # Start development server on localhost:5173
npm run build              # Build for production
npm run preview            # Preview production build
npm run check              # Run TypeScript checks
npm run check:watch        # Watch mode for TypeScript checks
```

## Architecture

### State Management
- **Svelte Stores**: Centralized state management in `src/lib/stores/index.ts`
- **Dual Instance System**: Separate data stores for inbound/outbound call types
- **Data Synchronization**: Automatic syncing between tabs (Rent Calculator → Arrangement Planner → Account Charges)

### Key Components Structure
- **RentCalculator.svelte**: Primary data entry with 13 calculation formulas
- **ArrangementPlanner.svelte**: Payment planning with debt clearance projections
- **AccountCharges.svelte**: UC/HB entitlement calculations with service charge breakdowns
- **Header.svelte**: Navigation with instance switching and session management
- **SessionLog/CaseLog.svelte**: Note management and case history

### Data Flow Pattern
1. Data entered in Rent Calculator updates the active instance store
2. Helper functions (`updateRentCalculatorData`, `updateArrangementPlannerData`, etc.) manage state updates
3. Derived stores (`currentInstanceData`) provide reactive data to components
4. All calculations are reactive and update automatically when input data changes

### Instance Management
- Two separate data stores: `inboundData` and `outboundData`
- Active instance determined by `activeInstance` store ('inbound' | 'outbound')
- Color-coded UI: Blue for inbound (#3B82F6), Green for outbound (#10B981)
- Independent data maintained for each instance type

### Mock Data System
Currently uses static/mock data with clear API integration points:
- Authentication: `login()` function in stores
- Data persistence: `saveInstanceData()` and `loadInstanceData()` functions
- All API calls are clearly marked with TODO comments for backend integration

### Calculation System
Implements 28 specific calculations across three modules:
- Rent Calculator (1-13): Rent conversions, payments, arrears
- Arrangement Planner (14-17): Debt clearance, weekly projections
- Account Charges (18-28): UC/HB entitlements, service charges

### Styling Approach
- **CSS Variables**: Centralized theming in `src/app.css`
- **LESS Preprocessing**: Component-scoped styles with global utility classes
- **Responsive Design**: Mobile-first approach with 768px breakpoint
- **Color System**: Status-based colors (green for positive, red for negative values)

## Key Development Patterns

### Store Updates
Always use the provided helper functions rather than direct store updates:
```typescript
updateRentCalculatorData({ weeklyRent: 100 });
updateNotes('New note content');
addSessionNote('Note content', 'tenant-ref');
```

### Component Communication
- Use stores for cross-component data sharing
- Custom events for parent-child communication (e.g., Header component events)
- Derived stores for computed values that need to be reactive

### Data Validation
- All calculations include null/undefined checks
- Numeric inputs are validated and converted appropriately
- Error states managed through `errorMessage` store

## Backend Integration Points

The application is designed for easy backend integration with these key areas:
- Replace mock authentication in `login()` function
- Implement API calls in data persistence functions
- Add environment variables for API endpoints
- Connect tenant management to database
- Implement persistent case log storage

## Testing Strategy

When adding tests:
- Focus on calculation accuracy in the store functions
- Test component rendering with different data states
- Verify data synchronization between instances
- Test responsive design at key breakpoints
- Validate accessibility features