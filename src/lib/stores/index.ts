import { writable, derived } from 'svelte/store';

// Types for our application state
export type InstanceType = 'inbound' | 'outbound';

export interface User {
  id: string;
  email: string;
  organizationId: string;
  organizationName: string;
  role: string;
}

export interface Tenant {
  id: string;
  reference: string;
  organizationId: string;
  createdBy: string;
  createdAt: Date;
}

export interface RentCalculatorData {
  reference: string;
  currentBalance: number;
  weeklyRent: number;
  tenantMonthlyPayment: number;
  apaHbMonthlyPayment: number;
  tpdMonthlyPayment: number;
  tenantWeeklyPayment: number;
  benefitsHbWeeklyPayment: number;
  scrfChecked: boolean;
  apaTpdChecked: boolean;
}

export interface ArrangementPlannerData {
  weeksToNextPay: number;
  paymentDue: number;
  timeframeWeeks: number;
}

export interface AccountChargesData {
  grossWeeklyRent: number;
  rentComponent: number;
  nonUcServiceCharge1: number;
  nonUcServiceCharge2: number;
  nonUcServiceCharge3: number;
}

export interface InstanceData {
  rentCalculator: RentCalculatorData;
  arrangementPlanner: ArrangementPlannerData;
  accountCharges: AccountChargesData;
  notes: string;
}

export interface SessionNote {
  id: string;
  tenantReference: string;
  instanceType: InstanceType;
  timestamp: Date;
  content: string;
}

// Default data structures
const defaultRentCalculatorData: RentCalculatorData = {
  reference: '',
  currentBalance: 0,
  weeklyRent: 0,
  tenantMonthlyPayment: 0,
  apaHbMonthlyPayment: 0,
  tpdMonthlyPayment: 0,
  tenantWeeklyPayment: 0,
  benefitsHbWeeklyPayment: 0,
  scrfChecked: false,
  apaTpdChecked: false
};

const defaultArrangementPlannerData: ArrangementPlannerData = {
  weeksToNextPay: 0,
  paymentDue: 0,
  timeframeWeeks: 0
};

const defaultAccountChargesData: AccountChargesData = {
  grossWeeklyRent: 0,
  rentComponent: 0,
  nonUcServiceCharge1: 0,
  nonUcServiceCharge2: 0,
  nonUcServiceCharge3: 0
};

const defaultInstanceData: InstanceData = {
  rentCalculator: { ...defaultRentCalculatorData },
  arrangementPlanner: { ...defaultArrangementPlannerData },
  accountCharges: { ...defaultAccountChargesData },
  notes: ''
};

// Authentication stores
export const currentUser = writable<User | null>(null);
export const isAuthenticated = derived(currentUser, ($user) => $user !== null);

// Instance management
export const activeInstance = writable<InstanceType>('inbound');

// Data stores for each instance
export const inboundData = writable<InstanceData>({ ...defaultInstanceData });
export const outboundData = writable<InstanceData>({ ...defaultInstanceData });

// Current active data (derived from activeInstance)
export const currentInstanceData = derived(
  [activeInstance, inboundData, outboundData],
  ([$activeInstance, $inboundData, $outboundData]) => {
    return $activeInstance === 'inbound' ? $inboundData : $outboundData;
  }
);

// Session log
export const sessionLog = writable<SessionNote[]>([]);

// Current tenant
export const currentTenant = writable<Tenant | null>(null);

// Loading states
export const isLoading = writable<boolean>(false);
export const loadingMessage = writable<string>('');

// Error handling
export const errorMessage = writable<string>('');

// Helper functions for updating instance data
export function updateRentCalculatorData(data: Partial<RentCalculatorData>) {
  activeInstance.subscribe(instance => {
    if (instance === 'inbound') {
      inboundData.update(current => ({
        ...current,
        rentCalculator: { ...current.rentCalculator, ...data }
      }));
    } else {
      outboundData.update(current => ({
        ...current,
        rentCalculator: { ...current.rentCalculator, ...data }
      }));
    }
  })();
}

export function updateArrangementPlannerData(data: Partial<ArrangementPlannerData>) {
  activeInstance.subscribe(instance => {
    if (instance === 'inbound') {
      inboundData.update(current => ({
        ...current,
        arrangementPlanner: { ...current.arrangementPlanner, ...data }
      }));
    } else {
      outboundData.update(current => ({
        ...current,
        arrangementPlanner: { ...current.arrangementPlanner, ...data }
      }));
    }
  })();
}

export function updateAccountChargesData(data: Partial<AccountChargesData>) {
  activeInstance.subscribe(instance => {
    if (instance === 'inbound') {
      inboundData.update(current => ({
        ...current,
        accountCharges: { ...current.accountCharges, ...data }
      }));
    } else {
      outboundData.update(current => ({
        ...current,
        accountCharges: { ...current.accountCharges, ...data }
      }));
    }
  })();
}

export function updateNotes(notes: string) {
  activeInstance.subscribe(instance => {
    if (instance === 'inbound') {
      inboundData.update(current => ({ ...current, notes }));
    } else {
      outboundData.update(current => ({ ...current, notes }));
    }
  })();
}

export function addSessionNote(content: string, tenantReference: string) {
  activeInstance.subscribe(instance => {
    const note: SessionNote = {
      id: crypto.randomUUID(),
      tenantReference,
      instanceType: instance,
      timestamp: new Date(),
      content
    };
    
    sessionLog.update(current => [...current, note]);
  })();
}

export function clearAllData() {
  inboundData.set({ ...defaultInstanceData });
  outboundData.set({ ...defaultInstanceData });
  sessionLog.set([]);
  currentTenant.set(null);
}

export function clearCurrentInstance() {
  activeInstance.subscribe(instance => {
    if (instance === 'inbound') {
      inboundData.set({ ...defaultInstanceData });
    } else {
      outboundData.set({ ...defaultInstanceData });
    }
  })();
}

// Mock authentication functions (to be replaced with API calls)
export async function login(email: string, password: string): Promise<boolean> {
  // TODO: Replace with actual API call
  isLoading.set(true);
  loadingMessage.set('Logging in...');
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock successful login
    const mockUser: User = {
      id: '1',
      email,
      organizationId: 'org-1',
      organizationName: 'Sample Council',
      role: 'agent'
    };
    
    currentUser.set(mockUser);
    return true;
  } catch (error) {
    errorMessage.set('Login failed. Please try again.');
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

export function logout() {
  currentUser.set(null);
  clearAllData();
}

// Mock data persistence functions (to be replaced with API calls)
export async function saveInstanceData(tenantId: string): Promise<boolean> {
  // TODO: Replace with actual API call to save calculator session data
  isLoading.set(true);
  loadingMessage.set('Saving data...');
  
  try {
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Mock: Saving instance data for tenant:', tenantId);
    return true;
  } catch (error) {
    errorMessage.set('Failed to save data. Please try again.');
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
}

export async function loadInstanceData(tenantId: string): Promise<boolean> {
  // TODO: Replace with actual API call to load calculator session data
  isLoading.set(true);
  loadingMessage.set('Loading data...');
  
  try {
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Mock: Loading instance data for tenant:', tenantId);
    return true;
  } catch (error) {
    errorMessage.set('Failed to load data. Please try again.');
    return false;
  } finally {
    isLoading.set(false);
    loadingMessage.set('');
  }
} 