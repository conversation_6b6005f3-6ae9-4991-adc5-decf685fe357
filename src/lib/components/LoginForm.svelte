<script lang="ts">
  import { login, isLoading, loadingMessage, errorMessage } from '$lib/stores';
  
  let email = '';
  let password = '';
  
  async function handleLogin() {
    if (!email || !password) {
      errorMessage.set('Please enter both email and password.');
      return;
    }
    
    const success = await login(email, password);
    if (!success) {
      password = ''; // Clear password on failed login
    }
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleLogin();
    }
  }
  
  // Clear error message when user starts typing
  $: if (email || password) {
    errorMessage.set('');
  }
  
  // Temporary function to fill test data
  function fillTestData() {
    email = '<EMAIL>';
    password = 'testpassword123';
  }
</script>

<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <img src="/Rent-Collection-Toolkit-Logo.svg" alt="Rent Collection Toolkit" class="login-logo">
      <p>Welcome back! Please sign in to continue</p>
    </div>
    
    <form class="login-form" on:submit|preventDefault={handleLogin}>
      <div class="form-group">
        <label for="email">Email Address</label>
        <input
          id="email"
          type="email"
          bind:value={email}
          on:keydown={handleKeydown}
          disabled={$isLoading}
          placeholder="Enter your email"
          required
        />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input
          id="password"
          type="password"
          bind:value={password}
          on:keydown={handleKeydown}
          disabled={$isLoading}
          placeholder="Enter your password"
          required
        />
      </div>
      
      <!-- Temporary test button for development -->
      <button
        type="button"
        class="btn-secondary test-button"
        on:click={fillTestData}
        disabled={$isLoading}
      >
        Fill Test Data
      </button>
      
      {#if $errorMessage}
        <div class="error-message">
          {$errorMessage}
        </div>
      {/if}
      
      <button
        type="submit"
        class="btn-primary login-button"
        disabled={$isLoading || !email || !password}
      >
        {#if $isLoading}
          <span class="spinner"></span>
          {$loadingMessage}
        {:else}
          Sign In
        {/if}
      </button>
    </form>
  </div>
</div>

<style>
  .login-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: #f8fafc;
    z-index: 1000;
  }

  .login-logo {
    width: 100%;
    margin-bottom: 30px;
  }

  .login-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.2);
    padding: 3rem;
    width: 100%;
    max-width: 420px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .login-header {
    text-align: center;
    margin-bottom: 2.5rem;
  }

  .login-header h1 {
    color: var(--color-primary);
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .login-header p {
    color: #6b7280;
    margin: 0;
    font-size: 1rem;
    font-weight: 400;
  }

  .login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .form-group input {
    width: 100%;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    outline: none;
  }

  .form-group input:focus {
    border-color: var(--color-primary);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(2, 85, 130, 0.1);
    transform: translateY(-2px);
  }

  .form-group input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .error-message {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #dc2626;
    padding: 1rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    border: 1px solid #f87171;
    animation: shake 0.5s ease-in-out;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  .login-button {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    margin-top: 0.5rem;
    background: var(--color-primary);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .login-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(2, 85, 130, 0.3);
  }

  .login-button:active {
    transform: translateY(0);
  }

  .login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .test-button {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.7);
    color: #374151;
    border: 2px solid #e5e7eb;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
  }
  
  .test-button:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.9);
    border-color: var(--color-primary);
    transform: translateY(-1px);
  }
  
  .test-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .login-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
  }

  .demo-note {
    background: linear-gradient(135deg, rgba(2, 85, 130, 0.1), rgba(2, 85, 130, 0.05));
    color: var(--color-primary);
    padding: 1rem;
    border-radius: 12px;
    font-size: 0.875rem;
    text-align: center;
    margin: 0;
    border: 1px solid rgba(2, 85, 130, 0.2);
  }

  .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Responsive Design */
  @media (max-width: 480px) {
    .login-container {
      padding: 1rem;
    }
    
    .login-card {
      padding: 2rem;
    }
    
    .login-header h1 {
      font-size: 1.5rem;
    }
  }
</style> 