<script lang="ts">
  import { updateArrangementPlannerData, currentInstanceData } from '$lib/stores';
  import NotesSection from './NotesSection.svelte';
  
  // Local state for form inputs
  let weeksToNextPay = 0;
  let paymentDue = 0;
  let timeframeWeeks = 0;
  
  // Synced data from Rent Calculator
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;
  $: weeklyRent = rentCalculatorData?.weeklyRent || 0;
  $: currentBalance = rentCalculatorData?.currentBalance || 0;
  $: tenantWeeklyPayment = rentCalculatorData?.tenantWeeklyPayment || 0;
  $: benefitsHbWeeklyPayment = rentCalculatorData?.benefitsHbWeeklyPayment || 0;
  $: reference = rentCalculatorData?.reference || '';
  
  // Subscribe to current instance data for arrangement planner
  $: if ($currentInstanceData) {
    const data = $currentInstanceData.arrangementPlanner;
    weeksToNextPay = data.weeksToNextPay;
    paymentDue = data.paymentDue;
    timeframeWeeks = data.timeframeWeeks;
  }
  
  // Calculation functions
  function calculateMonthlyRent(): number {
    return weeklyRent * 52 / 12;
  }
  
  function calculateMonthlyShortfallSurplus(): number {
    // Get this from rent calculator calculations
    const tenantMonthlyPayment = rentCalculatorData?.tenantMonthlyPayment || 0;
    const apaHbMonthlyPayment = rentCalculatorData?.apaHbMonthlyPayment || 0;
    const tpdMonthlyPayment = rentCalculatorData?.tpdMonthlyPayment || 0;
    
    const totalMonthlyPayments = tenantMonthlyPayment + apaHbMonthlyPayment + tpdMonthlyPayment + 
           (tenantWeeklyPayment * 52 / 12) + (benefitsHbWeeklyPayment * 52 / 12);
    
    return totalMonthlyPayments - calculateMonthlyRent();
  }
  
  // Calculation 14: Months to clear (current profile)
  function calculateMonthsToClear(): number {
    const mss = calculateMonthlyShortfallSurplus();
    if (mss <= 0 || currentBalance <= 0) {
      return 0;
    }
    return Math.ceil(currentBalance / mss);
  }
  
  // Calculation 16: Arrears after next payment
  function calculateArrearsAfterNextPayment(): number {
    return currentBalance + (weeklyRent * weeksToNextPay) - paymentDue;
  }
  
  // Calculation 17: Current rent plus (payment needed per week)
  function calculatePaymentNeededPerWeek(): number {
    if (timeframeWeeks <= 0) return 0;
    const arrearsAfter = calculateArrearsAfterNextPayment();
    return arrearsAfter / timeframeWeeks;
  }
  
  // Calculation 15: Weekly Projection
  function generateWeeklyProjection(): Array<{date: string, balance: number, isThreshold: boolean}> {
    const projections = [];
    let currentProjectedBalance = currentBalance;
    const totalWeeklyPayments = tenantWeeklyPayment + benefitsHbWeeklyPayment;
    
    // Start from next Monday (always the following Monday, even if today is Monday)
    const startDate = new Date();
    const currentDay = startDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    let daysUntilNextMonday;
    
    if (currentDay === 1) { // If today is Monday
      daysUntilNextMonday = 7; // Go to next Monday
    } else if (currentDay === 0) { // If today is Sunday
      daysUntilNextMonday = 1; // Go to tomorrow (Monday)
    } else { // If today is Tuesday-Saturday
      daysUntilNextMonday = (8 - currentDay) % 7;
    }
    
    startDate.setDate(startDate.getDate() + daysUntilNextMonday);
    
    // Generate 12 weeks of projections
    for (let week = 0; week < 12; week++) {
      const projectionDate = new Date(startDate);
      projectionDate.setDate(startDate.getDate() + (week * 7));
      
      // Calculate balance for this week
      currentProjectedBalance = currentProjectedBalance + weeklyRent - totalWeeklyPayments;
      
      // Check if this crosses threshold
      const fourWeeksThreshold = weeklyRent * 4;
      const eightWeeksThreshold = weeklyRent * 8;
      const isThreshold = currentProjectedBalance >= fourWeeksThreshold || currentProjectedBalance >= eightWeeksThreshold;
      
      projections.push({
        date: projectionDate.toLocaleDateString('en-GB', { 
          day: '2-digit', 
          month: '2-digit', 
          year: 'numeric' 
        }),
        balance: currentProjectedBalance,
        isThreshold
      });
    }
    
    return projections;
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function sanitizeNumericInput(value: string): number {
    const cleaned = value.trim().replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
  
  function handleInputChange() {
    updateArrangementPlannerData({
      weeksToNextPay,
      paymentDue,
      timeframeWeeks
    });
  }
  
  function handleNumericInput(event: Event, field: string) {
    const target = event.target as HTMLInputElement;
    const sanitizedValue = sanitizeNumericInput(target.value);
    
    switch(field) {
      case 'weeksToNextPay':
        weeksToNextPay = Math.max(0, Math.floor(sanitizedValue));
        break;
      case 'paymentDue':
        paymentDue = sanitizedValue;
        break;
      case 'timeframeWeeks':
        timeframeWeeks = Math.max(0, Math.floor(sanitizedValue));
        break;
    }
    
    handleInputChange();
  }
  
  function getBalanceClass(balance: number): string {
    const fourWeeksThreshold = weeklyRent * 4;
    const eightWeeksThreshold = weeklyRent * 8;
    
    if (balance >= eightWeeksThreshold) return 'balance-critical';
    if (balance >= fourWeeksThreshold) return 'balance-warning';
    return balance > 0 ? 'balance-debt' : 'balance-credit';
  }
</script>

<div class="arrangement-planner">
  <div class="planner-content">
    <h3>Arrangement Planner</h3>
    
    <!-- Data Synchronization Display -->
    <div class="sync-data">
      <h4>Synchronized Data from Rent Calculator</h4>
      <div class="sync-grid">
        <div class="sync-item">
          <label>Weekly Rent:</label>
          <span class="value">{formatCurrency(weeklyRent)}</span>
        </div>
        <div class="sync-item">
          <label>Current Balance:</label>
          <span class="value" style="color: {currentBalance > 0 ? '#dc2626' : currentBalance < 0 ? '#059669' : '#111827'}">{formatCurrency(currentBalance)}</span>
        </div>
        <div class="sync-item">
          <label>Monthly Rent:</label>
          <span class="value">{formatCurrency(calculateMonthlyRent())}</span>
        </div>
        <div class="sync-item">
          <label>Monthly Shortfall/Surplus:</label>
          <span class="value" style="color: {calculateMonthlyShortfallSurplus() > 0 ? '#059669' : calculateMonthlyShortfallSurplus() < 0 ? '#dc2626' : '#111827'}">{formatCurrency(calculateMonthlyShortfallSurplus())}</span>
        </div>
      </div>
    </div>
    
    <!-- Input Fields -->
    <div class="input-section">
      <h4>Payment Planning</h4>
      <div class="input-grid">
        <div class="input-group">
          <label for="weeksToNextPay">Weeks to next pay:</label>
          <input
            id="weeksToNextPay"
            type="number"
            min="0"
            step="1"
            value={weeksToNextPay}
            on:input={(e) => handleNumericInput(e, 'weeksToNextPay')}
          />
        </div>
        
        <div class="input-group">
          <label for="paymentDue">Payment due (at next payment):</label>
          <input
            id="paymentDue"
            type="number"
            min="0"
            step="0.01"
            value={paymentDue}
            on:input={(e) => handleNumericInput(e, 'paymentDue')}
          />
        </div>
        
        <div class="input-group">
          <label for="timeframeWeeks">Timeframe (to clear debt in weeks):</label>
          <input
            id="timeframeWeeks"
            type="number"
            min="0"
            step="1"
            value={timeframeWeeks}
            on:input={(e) => handleNumericInput(e, 'timeframeWeeks')}
          />
        </div>
      </div>
    </div>
    
    <!-- Output Fields -->
    <div class="output-section">
      <h4>Calculations</h4>
      <div class="output-grid">
        <div class="output-item">
          <label>Months to clear (current profile):</label>
          <span class="value">{calculateMonthsToClear()} months</span>
        </div>
        
        <div class="output-item">
          <label>Arrears after next payment:</label>
          <span class="value" style="color: {calculateArrearsAfterNextPayment() > 0 ? '#dc2626' : '#059669'}">{formatCurrency(calculateArrearsAfterNextPayment())}</span>
        </div>
        
        <div class="output-item">
          <label>Current rent plus (payment needed per week):</label>
          <span class="value">{formatCurrency(calculatePaymentNeededPerWeek())}</span>
        </div>
      </div>
    </div>
    
    <!-- Weekly Projection -->
    <div class="projection-section">
      <h4>Weekly Projection</h4>
      <div class="projection-info">
        <p>Projected balances for successive Mondays (Tenant Weekly: {formatCurrency(tenantWeeklyPayment)}, Benefits HB Weekly: {formatCurrency(benefitsHbWeeklyPayment)})</p>
      </div>
      <div class="projection-table-container">
        <table class="projection-table">
          <thead>
            <tr>
              <th>Date (Monday)</th>
              <th>Projected Balance</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            {#each generateWeeklyProjection() as projection}
              <tr class={getBalanceClass(projection.balance)}>
                <td>{projection.date}</td>
                <td>{formatCurrency(projection.balance)}</td>
                <td>
                  {#if projection.balance >= weeklyRent * 8}
                    8+ weeks arrears
                  {:else if projection.balance >= weeklyRent * 4}
                    4+ weeks arrears
                  {:else if projection.balance > 0}
                    In debt
                  {:else if projection.balance < 0}
                    In credit
                  {:else}
                    Balanced
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Notes Section -->
    <NotesSection tenantReference={reference} />
  </div>
</div>

<style>
  .arrangement-planner {
    padding: 1.5rem;
  }

  .planner-content h3 {
    margin: 0 0 1.5rem 0;
    color: #111827;
  }

  .planner-content h4 {
    margin: 1.5rem 0 1rem 0;
    color: #1f2937;
    font-size: 1.1rem;
  }

  /* Synchronized Data Section */
  .sync-data {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .sync-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
  }

  .sync-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: white;
    border-radius: 0.125rem;
  }

  .sync-item label {
    font-weight: 500;
    color: #374151;
  }

  .sync-item .value {
    font-weight: 600;
    color: #111827;
  }

  /* Input Section */
  .input-section {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .input-group label {
    font-weight: 500;
    color: #374151;
  }

  .input-group input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.125rem;
    font-size: 1rem;
  }

  .input-group input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  /* Output Section */
  .output-section {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .output-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 0.75rem;
  }

  .output-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.125rem;
  }

  .output-item label {
    font-weight: 500;
    color: #374151;
  }

  .output-item .value {
    font-weight: 600;
    color: #111827;
  }

  /* Projection Section */
  .projection-section {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .projection-info {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #dbeafe;
    border-radius: 0.125rem;
    color: #1e40af;
  }

  .projection-table-container {
    overflow-x: auto;
  }

  .projection-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem;
  }

  .projection-table th,
  .projection-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  .projection-table th {
    background-color: #f3f4f6;
    font-weight: 600;
    color: #1f2937;
  }

  .projection-table tr:hover {
    background-color: #f9fafb;
  }

  /* Balance status classes */
  .balance-critical {
    background-color: #fef2f2;
    color: #7f1d1d;
  }

  .balance-critical:hover {
    background-color: #fee2e2;
  }

  .balance-warning {
    background-color: #fffbeb;
    color: #92400e;
  }

  .balance-warning:hover {
    background-color: #fef3c7;
  }

  .balance-debt {
    background-color: #fef7f7;
  }

  .balance-credit {
    background-color: #f0fdf4;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .arrangement-planner {
      padding: 1rem;
    }

    .sync-grid,
    .input-grid,
    .output-grid {
      grid-template-columns: 1fr;
    }

    .sync-item,
    .output-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    .projection-table {
      font-size: 0.9rem;
    }

    .projection-table th,
    .projection-table td {
      padding: 0.5rem;
    }
  }
</style> 