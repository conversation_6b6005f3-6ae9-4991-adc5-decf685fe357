<script lang="ts">
  import { updateNotes, addSessionNote, currentInstanceData, activeInstance } from '$lib/stores';
  
  export let tenantReference: string = '';
  
  let notes = '';
  
  // Subscribe to current instance data to get notes
  $: if ($currentInstanceData) {
    notes = $currentInstanceData.notes;
  }
  
  function handleNotesChange() {
    updateNotes(notes);
  }
  
  async function saveNotes() {
    if (!notes.trim()) {
      return;
    }
    
    try {
      // Copy to clipboard
      await navigator.clipboard.writeText(notes);
      
      // Add to session log
      addSessionNote(notes, tenantReference);
      
      // TODO: Send to backend for persistent CaseLog storage
      console.log('Mock: Saving notes to CaseLog for tenant:', tenantReference);
      
      // Show success feedback (could be a toast notification)
      console.log('Notes saved to clipboard and session log');
      
    } catch (error) {
      console.error('Failed to copy notes to clipboard:', error);
      // Still add to session log even if clipboard fails
      addSessionNote(notes, tenantReference);
    }
  }
  
  function clearNotes() {
    notes = '';
    updateNotes('');
  }
  
  function handleDictate() {
    // TODO: Implement speech-to-text functionality
    console.log('Dictate functionality - to be implemented');
    alert('Speech-to-text functionality will be implemented in a future update.');
  }
</script>

<div class="notes-section">
  <h4>Notes</h4>
  
  <div class="notes-content">
    <textarea
      bind:value={notes}
      on:input={handleNotesChange}
      placeholder="Enter your notes here..."
      rows="6"
      class="notes-textarea"
    ></textarea>
    
    <div class="notes-actions">
      <button
        class="btn-primary"
        on:click={saveNotes}
        disabled={!notes.trim()}
      >
        Save Notes
      </button>
      
      <button
        class="btn-secondary"
        on:click={handleDictate}
      >
        Dictate
      </button>
      
      <button
        class="btn-warning"
        on:click={clearNotes}
        disabled={!notes.trim()}
      >
        Clear Notes
      </button>
    </div>
    
    <div class="notes-info">
      <p class="info-text">
        <strong>Save Notes:</strong> Copies to clipboard and adds to session log
      </p>
      <p class="info-text">
        <strong>Instance:</strong> 
        <span class="instance-badge {$activeInstance}">
          {$activeInstance === 'inbound' ? 'Inbound' : 'Outbound'}
        </span>
      </p>
    </div>
  </div>
</div>

<style>
  .notes-section {
    margin-top: var(--spacing-6);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--color-gray-200);
  }

  .notes-section h4 {
    margin: 0 0 var(--spacing-4) 0;
    color: var(--color-gray-900);
  }

  .notes-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .notes-textarea {
    width: 100%;
    min-height: 120px;
    resize: vertical;
    font-family: var(--font-family-sans);
    line-height: 1.5;
  }

  .notes-actions {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
  }

  .notes-actions button {
    flex: 0 0 auto;
  }

  .notes-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    background-color: var(--color-gray-50);
    border-radius: var(--radius-md);
  }

  .info-text {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
  }

  .instance-badge {
    display: inline-block;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-base);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
  }

  .instance-badge.inbound {
    background-color: var(--color-inbound-light);
    color: var(--color-inbound);
  }

  .instance-badge.outbound {
    background-color: var(--color-outbound-light);
    color: var(--color-outbound);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .notes-actions {
      flex-direction: column;
    }

    .notes-actions button {
      flex: 1;
    }

    .notes-info {
      flex-direction: column;
    }
  }
</style> 