<script lang="ts">
  import { sessionLog, currentInstanceData } from '$lib/stores';
  import type { SessionNote } from '$lib/stores';
  import Modal from './Modal.svelte';
  
  export let isOpen = false;
  
  // Mock persistent case log data (in real implementation, this would come from backend)
  let mockCaseLogData: SessionNote[] = [
    {
      id: 'mock-1',
      tenantReference: '12345',
      instanceType: 'inbound',
      timestamp: new Date('2024-01-15T09:30:00'),
      content: 'Initial contact made. Tenant explained financial difficulties due to job loss.'
    },
    {
      id: 'mock-2',
      tenantReference: '12345',
      instanceType: 'outbound',
      timestamp: new Date('2024-01-15T14:15:00'),
      content: 'Follow-up call. Discussed payment arrangement options. Tenant interested in weekly payments.'
    },
    {
      id: 'mock-3',
      tenantReference: '12345',
      instanceType: 'inbound',
      timestamp: new Date('2024-01-16T11:00:00'),
      content: 'Payment arrangement agreed: £25 per week starting next Monday. Tenant confirmed understanding.'
    },
    {
      id: 'mock-4',
      tenantReference: '67890',
      instanceType: 'inbound',
      timestamp: new Date('2024-01-16T15:30:00'),
      content: 'Tenant called regarding arrears notice. Explained current balance and payment options.'
    },
    {
      id: 'mock-5',
      tenantReference: '67890',
      instanceType: 'outbound',
      timestamp: new Date('2024-01-17T10:45:00'),
      content: 'Outbound call to confirm payment received. Tenant making good progress on arrangement.'
    }
  ];
  
  // Get current tenant reference for filtering
  $: currentReference = $currentInstanceData?.rentCalculator?.reference || '';
  
  // Combine session log with mock persistent data
  $: allNotes = [...mockCaseLogData, ...$sessionLog].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );
  
  // Filter options
  let filterByTenant = true;
  let filterByInstance: 'all' | 'inbound' | 'outbound' = 'all';
  let searchTerm = '';
  
  // Filtered notes
  $: filteredNotes = allNotes.filter(note => {
    // Filter by tenant reference
    if (filterByTenant && currentReference && note.tenantReference !== currentReference) {
      return false;
    }
    
    // Filter by instance type
    if (filterByInstance !== 'all' && note.instanceType !== filterByInstance) {
      return false;
    }
    
    // Filter by search term
    if (searchTerm && !note.content.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    return true;
  });
  
  function formatTimestamp(date: Date): string {
    return new Date(date).toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  function getInstanceClass(instanceType: string): string {
    return instanceType === 'inbound' ? 'instance-inbound' : 'instance-outbound';
  }
  
  function getInstanceDisplayName(instanceType: string): string {
    return instanceType === 'inbound' ? 'Inbound' : 'Outbound';
  }
  
  
  function exportNotes() {
    const exportData = filteredNotes.map(note => ({
      'Tenant Reference': note.tenantReference,
      'Instance Type': getInstanceDisplayName(note.instanceType),
      'Timestamp': formatTimestamp(note.timestamp),
      'Content': note.content
    }));
    
    const csvContent = [
      Object.keys(exportData[0]).join(','),
      ...exportData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `caselog-${currentReference || 'all'}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }
</script>

<Modal bind:isOpen title="Case Log" size="xl">
  <!-- Filters and Search -->
  <div class="filters-section">
    <div class="filter-group">
      <label class="checkbox-label">
        <input type="checkbox" bind:checked={filterByTenant} />
        Filter by current tenant ({currentReference || 'No reference set'})
      </label>
    </div>
    
    <div class="filter-group">
      <label for="instanceFilter">Instance Type:</label>
      <select id="instanceFilter" bind:value={filterByInstance}>
        <option value="all">All Instances</option>
        <option value="inbound">Inbound Only</option>
        <option value="outbound">Outbound Only</option>
      </select>
    </div>
    
    <div class="filter-group">
      <label for="searchTerm">Search Notes:</label>
      <input
        id="searchTerm"
        type="text"
        placeholder="Search in note content..."
        bind:value={searchTerm}
      />
    </div>
    
    <div class="filter-actions">
      <button class="btn-secondary btn-sm" on:click={exportNotes}>
        Export CSV
      </button>
    </div>
  </div>
  
  <!-- Notes Display -->
  <div class="notes-section">
    <div class="notes-header">
      <h3>Notes History ({filteredNotes.length} entries)</h3>
      <div class="legend">
        <span class="legend-item">
          <span class="legend-dot instance-inbound"></span>
          Inbound
        </span>
        <span class="legend-item">
          <span class="legend-dot instance-outbound"></span>
          Outbound
        </span>
      </div>
    </div>
    
    {#if filteredNotes.length === 0}
      <div class="empty-state">
        <div class="empty-icon">📭</div>
        <h4>No notes found</h4>
        <p>No notes match your current filters.</p>
        {#if searchTerm}
          <p>Try adjusting your search term or filters.</p>
        {/if}
      </div>
    {:else}
      <div class="notes-list">
        {#each filteredNotes as note}
          <div class="note-item {getInstanceClass(note.instanceType)}">
            <div class="note-header">
              <div class="note-meta">
                <span class="tenant-ref">Ref: {note.tenantReference}</span>
                <span class="instance-badge {getInstanceClass(note.instanceType)}">
                  {getInstanceDisplayName(note.instanceType)}
                </span>
                <span class="timestamp">{formatTimestamp(note.timestamp)}</span>
              </div>
            </div>
            <div class="note-content">
              {note.content}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
  
  <!-- Info Section -->
  <div class="info-section">
    <div class="info-card">
      <div class="info-header">
        <h4>About Case Log</h4>
        <div class="info-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 11H5a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h2m0 0V9a2 2 0 0 1 2-2h2M7 16h10m4-4a2 2 0 0 0-2-2h-1M7 16V5a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v6.5"/>
          </svg>
        </div>
      </div>
      <p>
        The Case Log provides a persistent, searchable history of all notes and interactions 
        for tenants. In the full implementation, this data would be stored securely in the 
        backend database and synchronized across all users in your organization.
      </p>
      <div class="info-features">
        <div class="feature-item">
          <div class="feature-icon">📝</div>
          <div class="feature-text">
            <strong>Session Notes:</strong> Notes from your current session
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">💾</div>
          <div class="feature-text">
            <strong>Persistent Notes:</strong> Historical notes saved to database
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">🔍</div>
          <div class="feature-text">
            <strong>Search & Filter:</strong> Find specific interactions quickly
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">📊</div>
          <div class="feature-text">
            <strong>Export:</strong> Download notes for reporting
          </div>
        </div>
      </div>
    </div>
  </div>
</Modal>

<style>
  /* Filters Section */
  .filters-section {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    border: 1px solid rgba(2, 85, 130, 0.1);
    box-shadow: 
      0 4px 6px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-group label {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    text-transform: none;
    font-weight: 500;
  }

  .filter-group input,
  .filter-group select {
    padding: 0.875rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    font-size: 0.875rem;
    background: #ffffff;
    transition: all 0.25s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .filter-group input:focus,
  .filter-group select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 
      0 0 0 3px rgba(2, 85, 130, 0.1),
      inset 0 1px 3px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }

  .filter-actions {
    display: flex;
    align-items: end;
  }

  /* Notes Section */
  .notes-section {
    margin-bottom: 2rem;
  }

  .notes-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
  }

  .notes-header h3 {
    margin: 0;
    color: var(--color-primary);
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .legend {
    display: flex;
    gap: 1.5rem;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
  }

  .legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .legend-dot.instance-inbound {
    background: linear-gradient(135deg, var(--color-primary), rgba(2, 85, 130, 0.7));
  }

  .legend-dot.instance-outbound {
    background: linear-gradient(135deg, var(--color-secondary), rgba(0, 51, 68, 0.7));
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .empty-state h4 {
    margin: 0 0 0.5rem 0;
    color: var(--color-primary);
    font-size: 1.25rem;
  }

  .empty-state p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
  }

  .notes-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
  }

  .note-item {
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    padding: 2rem;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    box-shadow: 
      0 4px 6px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: all 0.25s ease;
    position: relative;
    overflow: hidden;
  }

  .note-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(180deg, var(--color-primary), var(--color-secondary));
    border-radius: 0 6px 6px 0;
  }

  .note-item.instance-inbound::before {
    background: linear-gradient(180deg, var(--color-primary), rgba(2, 85, 130, 0.7));
  }

  .note-item.instance-outbound::before {
    background: linear-gradient(180deg, var(--color-secondary), rgba(0, 51, 68, 0.7));
  }

  .note-item:hover {
    transform: translateY(-3px);
    box-shadow: 
      0 12px 24px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .note-header {
    margin-bottom: 1rem;
  }

  .note-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .tenant-ref {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 0.875rem;
  }

  .instance-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
  }

  .instance-badge.instance-inbound {
    background: linear-gradient(135deg, rgba(2, 85, 130, 0.15), rgba(2, 85, 130, 0.25));
    color: var(--color-primary);
    border: 1px solid rgba(2, 85, 130, 0.3);
  }

  .instance-badge.instance-outbound {
    background: linear-gradient(135deg, rgba(0, 51, 68, 0.15), rgba(0, 51, 68, 0.25));
    color: var(--color-secondary);
    border: 1px solid rgba(0, 51, 68, 0.3);
  }

  .timestamp {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .note-content {
    color: #374151;
    font-size: 1rem;
    line-height: 1.6;
    padding-left: 1rem;
  }

  /* Info Section */
  .info-section {
    margin-top: 2rem;
  }

  .info-card {
    background: linear-gradient(135deg, rgba(2, 85, 130, 0.03), rgba(2, 85, 130, 0.08));
    padding: 2.5rem;
    border-radius: 24px;
    border: 1px solid rgba(2, 85, 130, 0.1);
    box-shadow: 
      0 8px 16px rgba(2, 85, 130, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .info-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .info-header h4 {
    margin: 0;
    color: var(--color-primary);
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .info-icon {
    color: var(--color-primary);
    opacity: 0.7;
  }

  .info-card p {
    margin: 0 0 2rem 0;
    color: #64748b;
    font-size: 1rem;
    line-height: 1.6;
  }

  .info-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .feature-icon {
    font-size: 1.25rem;
  }

  .feature-text {
    color: #475569;
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .feature-text strong {
    color: var(--color-primary);
    font-weight: 600;
  }

  /* Button Styles */
  .btn-secondary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 16px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.25s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .btn-secondary:hover {
    background: var(--color-primary);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(2, 85, 130, 0.2);
  }

  .btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  /* Scrollbar styling */
  .notes-list::-webkit-scrollbar {
    width: 8px;
  }

  .notes-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .notes-list::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--color-primary), var(--color-secondary));
    border-radius: 4px;
  }

  .notes-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--color-secondary), var(--color-primary));
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .filters-section {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .notes-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .legend {
      gap: 1rem;
    }

    .note-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .info-features {
      grid-template-columns: 1fr;
    }
  }
</style>