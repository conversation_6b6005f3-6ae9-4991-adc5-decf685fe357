<script lang="ts">
  import { sessionLog, clearAllData } from '$lib/stores';
  import type { SessionNote } from '$lib/stores';
  import Modal from './Modal.svelte';
  import { FileText, Phone, Smartphone, Download, Trash2, Bell, Clock, Clipboard, AlertTriangle, Save } from 'lucide-svelte';
  
  export let isOpen = false;
  
  function formatTimestamp(date: Date): string {
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }
  
  function getInstanceClass(instanceType: string): string {
    return instanceType === 'inbound' ? 'instance-inbound' : 'instance-outbound';
  }
  
  function getInstanceDisplayName(instanceType: string): string {
    return instanceType === 'inbound' ? 'Inbound' : 'Outbound';
  }
  
  function handleClearSession() {
    if (confirm('Are you sure you want to clear the entire session? This will remove all data and notes.')) {
      clearAllData();
    }
  }
  
  async function exportSessionLog() {
    if ($sessionLog.length === 0) {
      alert('No session data to export.');
      return;
    }
    
    const exportData = $sessionLog.map(note => 
      `[${note.tenantReference || 'No Ref'}] [${getInstanceDisplayName(note.instanceType)}] [${formatTimestamp(note.timestamp)}]\n${note.content}\n`
    ).join('\n---\n\n');
    
    try {
      await navigator.clipboard.writeText(exportData);
      alert('Session log copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy session log:', error);
      // Fallback: create downloadable file
      const blob = new Blob([exportData], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `session-log-${new Date().toISOString().split('T')[0]}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  }
</script>

<Modal bind:isOpen title="Session Log" size="large">
  <div class="session-overview">
    <div class="session-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <FileText size={32} />
        </div>
        <div class="stat-info">
          <span class="stat-value">{$sessionLog.length}</span>
          <span class="stat-label">Total Notes</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <Phone size={32} />
        </div>
        <div class="stat-info">
          <span class="stat-value instance-inbound">
            {$sessionLog.filter(note => note.instanceType === 'inbound').length}
          </span>
          <span class="stat-label">Inbound</span>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <Smartphone size={32} />
        </div>
        <div class="stat-info">
          <span class="stat-value instance-outbound">
            {$sessionLog.filter(note => note.instanceType === 'outbound').length}
          </span>
          <span class="stat-label">Outbound</span>
        </div>
      </div>
    </div>
    
    <div class="session-actions">
      <button class="btn-secondary" on:click={exportSessionLog}>
        <Download size={16} />
        Export Log
      </button>
      <button class="btn-warning" on:click={handleClearSession}>
        <Trash2 size={16} />
        Clear Session
      </button>
    </div>
  </div>
  
  <div class="session-entries">
    {#if $sessionLog.length === 0}
      <div class="empty-state">
        <div class="empty-icon">
          <Bell size={48} />
        </div>
        <h4>No session notes yet</h4>
        <p>Notes will appear here as you add them during your session.</p>
        <p>Use the "Save Notes" button in any tab to add entries.</p>
      </div>
    {:else}
      <div class="entries-list">
        {#each $sessionLog.slice().reverse() as note (note.id)}
          <div class="session-entry {getInstanceClass(note.instanceType)}">
            <div class="entry-header">
              <div class="entry-meta">
                <span class="tenant-ref">
                  {note.tenantReference || 'No Reference'}
                </span>
                <span class="instance-badge {note.instanceType}">
                  {getInstanceDisplayName(note.instanceType)}
                </span>
                <span class="timestamp">
                  {formatTimestamp(note.timestamp)}
                </span>
              </div>
            </div>
            <div class="entry-content">
              {note.content}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
  
  <div class="info-section">
    <div class="info-card">
      <div class="info-header">
        <h4>About Session Log</h4>
        <div class="info-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        </div>
      </div>
      <p>
        The Session Log shows all notes and interactions from your current session. 
        These notes are temporary and will be lost when you refresh or close the application 
        unless saved to the Case Log.
      </p>
      <div class="info-features">
        <div class="feature-item">
          <div class="feature-icon">
            <Clock size={20} />
          </div>
          <div class="feature-text">
            <strong>Real-time:</strong> Notes appear instantly as you add them
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <Clipboard size={20} />
          </div>
          <div class="feature-text">
            <strong>Export:</strong> Copy session notes to clipboard for external use
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <AlertTriangle size={20} />
          </div>
          <div class="feature-text">
            <strong>Temporary:</strong> Session data clears on refresh or logout
          </div>
        </div>
        <div class="feature-item">
          <div class="feature-icon">
            <Save size={20} />
          </div>
          <div class="feature-text">
            <strong>Save:</strong> Important notes should be saved to Case Log
          </div>
        </div>
      </div>
    </div>
  </div>
</Modal>

<style>
  /* Session Overview */
  .session-overview {
    margin-bottom: 2rem;
  }

  .session-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 1.5rem;
    border-radius: 20px;
    border: 1px solid rgba(2, 85, 130, 0.1);
    box-shadow: 
      0 4px 6px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.25s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 
      0 8px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .stat-icon {
    color: var(--color-primary);
    opacity: 0.8;
  }

  .stat-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
  }

  .stat-value.instance-inbound {
    color: var(--color-primary);
  }

  .stat-value.instance-outbound {
    color: var(--color-secondary);
  }

  .stat-label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .session-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  /* Session Entries */
  .session-entries {
    margin-bottom: 2rem;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
  }

  .empty-icon {
    margin-bottom: 1rem;
    color: #94a3b8;
  }

  .empty-state h4 {
    margin: 0 0 0.5rem 0;
    color: var(--color-primary);
    font-size: 1.25rem;
  }

  .empty-state p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
  }

  .entries-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
  }

  .session-entry {
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    padding: 1.5rem;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    box-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: all 0.25s ease;
    position: relative;
    overflow: hidden;
  }

  .session-entry::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--color-primary), var(--color-secondary));
    border-radius: 0 4px 4px 0;
  }

  .session-entry.instance-inbound::before {
    background: linear-gradient(180deg, var(--color-primary), rgba(2, 85, 130, 0.7));
  }

  .session-entry.instance-outbound::before {
    background: linear-gradient(180deg, var(--color-secondary), rgba(0, 51, 68, 0.7));
  }

  .session-entry:hover {
    transform: translateY(-1px);
    box-shadow: 
      0 6px 12px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .entry-header {
    margin-bottom: 0.75rem;
  }

  .entry-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .tenant-ref {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 0.875rem;
  }

  .instance-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .instance-badge.inbound {
    background: linear-gradient(135deg, rgba(2, 85, 130, 0.15), rgba(2, 85, 130, 0.25));
    color: var(--color-primary);
    border: 1px solid rgba(2, 85, 130, 0.3);
  }

  .instance-badge.outbound {
    background: linear-gradient(135deg, rgba(0, 51, 68, 0.15), rgba(0, 51, 68, 0.25));
    color: var(--color-secondary);
    border: 1px solid rgba(0, 51, 68, 0.3);
  }

  .timestamp {
    color: #64748b;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .entry-content {
    color: #374151;
    font-size: 0.875rem;
    line-height: 1.5;
    padding-left: 0.75rem;
  }

  /* Info Section */
  .info-section {
    margin-top: 2rem;
  }

  .info-card {
    background: linear-gradient(135deg, rgba(2, 85, 130, 0.03), rgba(2, 85, 130, 0.08));
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid rgba(2, 85, 130, 0.1);
    box-shadow: 
      0 6px 12px rgba(2, 85, 130, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .info-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .info-header h4 {
    margin: 0;
    color: var(--color-primary);
    font-size: 1.125rem;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .info-icon {
    color: var(--color-primary);
    opacity: 0.7;
  }

  .info-card p {
    margin: 0 0 1.5rem 0;
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .info-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
  }

  .feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .feature-icon {
    color: var(--color-primary);
    opacity: 0.7;
  }

  .feature-text {
    color: #475569;
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .feature-text strong {
    color: var(--color-primary);
    font-weight: 600;
  }

  /* Button Styles */
  .btn-secondary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
    padding: 0.75rem 1.25rem;
    border-radius: 14px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.25s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-secondary:hover {
    background: var(--color-primary);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(2, 85, 130, 0.2);
  }

  .btn-warning {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: var(--color-red);
    border: 2px solid var(--color-red);
    padding: 0.75rem 1.25rem;
    border-radius: 14px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.25s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-warning:hover {
    background: var(--color-red);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(220, 38, 38, 0.2);
  }

  /* Scrollbar styling */
  .entries-list::-webkit-scrollbar {
    width: 6px;
  }

  .entries-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  .entries-list::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--color-primary), var(--color-secondary));
    border-radius: 3px;
  }

  .entries-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--color-secondary), var(--color-primary));
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .session-stats {
      grid-template-columns: 1fr;
    }

    .session-actions {
      flex-direction: column;
      align-items: stretch;
    }

    .entry-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .info-features {
      grid-template-columns: 1fr;
    }
  }
</style>