<script lang="ts">
  import { updateRentCalculatorData, currentInstanceData } from '$lib/stores';
  import NotesSection from './NotesSection.svelte';
  
  // Local state for form inputs
  let reference = '';
  let currentBalance = 0;
  let weeklyRent = 0;
  let tenantMonthlyPayment = 0;
  let apaHbMonthlyPayment = 0;
  let tpdMonthlyPayment = 0;
  let tenantWeeklyPayment = 0;
  let benefitsHbWeeklyPayment = 0;
  let scrfChecked = false;
  let apaTpdChecked = false;
  
  // Subscribe to current instance data
  $: if ($currentInstanceData) {
    const data = $currentInstanceData.rentCalculator;
    reference = data.reference;
    currentBalance = data.currentBalance;
    weeklyRent = data.weeklyRent;
    tenantMonthlyPayment = data.tenantMonthlyPayment;
    apaHbMonthlyPayment = data.apaHbMonthlyPayment;
    tpdMonthlyPayment = data.tpdMonthlyPayment;
    tenantWeeklyPayment = data.tenantWeeklyPayment;
    benefitsHbWeeklyPayment = data.benefitsHbWeeklyPayment;
    scrfChecked = data.scrfChecked;
    apaTpdChecked = data.apaTpdChecked;
  }
  
  // Calculation functions
  function calculateMonthlyRent(): number {
    return weeklyRent * 52 / 12;
  }
  
  function calculateTotalMonthlyPayments(): number {
    return tenantMonthlyPayment + apaHbMonthlyPayment + tpdMonthlyPayment + 
           (tenantWeeklyPayment * 52 / 12) + (benefitsHbWeeklyPayment * 52 / 12);
  }
  
  function calculateTotalWeeklyPayments(): number {
    return tenantWeeklyPayment + benefitsHbWeeklyPayment + 
           (tenantMonthlyPayment * 12 / 52) + (apaHbMonthlyPayment * 12 / 52) + 
           (tpdMonthlyPayment * 12 / 52);
  }
  
  function calculateMonthlyShortfallSurplus(): number {
    return calculateTotalMonthlyPayments() - calculateMonthlyRent();
  }
  
  function calculateWeeklyShortfallSurplus(): number {
    return calculateTotalWeeklyPayments() - weeklyRent;
  }
  
  function calculateTenantContributionMonthly(): number {
    return tenantMonthlyPayment + (tenantWeeklyPayment * 52 / 12);
  }
  
  function calculateTenantContributionWeekly(): number {
    return tenantWeeklyPayment + (tenantMonthlyPayment * 12 / 52);
  }
  
  function calculateBenefitsContributionMonthly(): number {
    return apaHbMonthlyPayment + tpdMonthlyPayment + (benefitsHbWeeklyPayment * 52 / 12);
  }
  
  function calculateBenefitsContributionWeekly(): number {
    return benefitsHbWeeklyPayment + ((apaHbMonthlyPayment + tpdMonthlyPayment) * 12 / 52);
  }
  
  function calculateArrearsClearDate(): string {
    const monthlyShortfall = calculateMonthlyShortfallSurplus();
    if (monthlyShortfall <= 0 || currentBalance <= 0) {
      return 'N/A';
    }
    
    const monthsToClearing = Math.ceil(currentBalance / monthlyShortfall);
    const clearingDate = new Date();
    clearingDate.setMonth(clearingDate.getMonth() + monthsToClearing);
    
    return clearingDate.toLocaleDateString('en-GB', { 
      month: 'long', 
      year: 'numeric' 
    });
  }
  
  function calculate4WeeksArrears(): number {
    return weeklyRent * 4;
  }
  
  function calculate8WeeksArrears(): number {
    return weeklyRent * 8;
  }
  
  function calculateRefund(): number {
    if (currentBalance >= 0) return 0;
    return Math.abs(currentBalance) - weeklyRent;
  }
  
  function getAccountStatus(): string {
    if (currentBalance > 0) {
      return 'This account is in debt, i.e. positive balance';
    } else if (currentBalance < 0) {
      return 'This account is in credit, i.e. negative balance';
    }
    return 'This account is balanced';
  }
  
  function getShortfallSurplusColor(value: number): string {
    if (value > 0) return '#059669';
    if (value < 0) return '#dc2626';
    return '#111827';
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function sanitizeNumericInput(value: string): number {
    // Remove leading/trailing spaces and commas
    const cleaned = value.trim().replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
  
  function handleInputChange() {
    updateRentCalculatorData({
      reference: reference.trim(),
      currentBalance,
      weeklyRent,
      tenantMonthlyPayment,
      apaHbMonthlyPayment,
      tpdMonthlyPayment,
      tenantWeeklyPayment,
      benefitsHbWeeklyPayment,
      scrfChecked,
      apaTpdChecked
    });
  }
  
  function handleNumericInput(event: Event, field: string) {
    const target = event.target as HTMLInputElement;
    const sanitizedValue = sanitizeNumericInput(target.value);
    
    switch(field) {
      case 'currentBalance':
        currentBalance = sanitizedValue;
        break;
      case 'weeklyRent':
        weeklyRent = sanitizedValue;
        break;
      case 'tenantMonthlyPayment':
        tenantMonthlyPayment = sanitizedValue;
        break;
      case 'apaHbMonthlyPayment':
        apaHbMonthlyPayment = sanitizedValue;
        break;
      case 'tpdMonthlyPayment':
        tpdMonthlyPayment = sanitizedValue;
        break;
      case 'tenantWeeklyPayment':
        tenantWeeklyPayment = sanitizedValue;
        break;
      case 'benefitsHbWeeklyPayment':
        benefitsHbWeeklyPayment = sanitizedValue;
        break;
    }
    
    handleInputChange();
  }
  
  async function handleRentButton() {
    // Copy preset text as per original specification
    const presetText = `Weekly rent is ${weeklyRent.toFixed(2)}, monthly rent is ${calculateMonthlyRent().toFixed(2)}`;
    try {
      await navigator.clipboard.writeText(presetText);
      console.log('Rent information copied to clipboard');
    } catch (error) {
      console.error('Failed to copy rent information:', error);
    }
  }
  
  function handleUpdate() {
    // Trigger all calculations (they're reactive, so this just forces an update)
    handleInputChange();
  }
  
  async function copyReference() {
    if (reference) {
      try {
        await navigator.clipboard.writeText(reference);
        console.log('Reference copied to clipboard');
      } catch (error) {
        console.error('Failed to copy reference:', error);
      }
    }
  }
</script>

<div class="rent-calculator">
  <div class="calculator-content">
    <h3>Rent Calculator</h3>
    
    <div class="calculator-grid">
      <!-- Input Section -->
      <div class="input-section">
        <h4>Input Details</h4>
        
        <div class="form-row">
          <div class="form-group">
            <label for="reference">Reference</label>
            <div class="input-with-button">
              <input
                id="reference"
                type="text"
                bind:value={reference}
                on:input={handleInputChange}
                placeholder="Enter reference"
              />
              <button
                class="copy-button"
                on:click={copyReference}
                disabled={!reference}
                title="Copy reference to clipboard"
              >
                Copy
              </button>
            </div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="currentBalance">Current Balance (£)</label>
            <input
              id="currentBalance"
              type="text"
              bind:value={currentBalance}
              on:input={(e) => handleNumericInput(e, 'currentBalance')}
              placeholder="0.00"
            />
          </div>
          
          <div class="form-group">
            <label for="weeklyRent">Rent (Weekly) (£)</label>
            <div class="input-with-button">
              <input
                id="weeklyRent"
                type="text"
                bind:value={weeklyRent}
                on:input={(e) => handleNumericInput(e, 'weeklyRent')}
                placeholder="0.00"
              />
              <button
                class="copy-button"
                on:click={handleRentButton}
                title="Copy rent information to clipboard"
              >
                Copy
              </button>
            </div>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="tenantMonthly">Tenant (Monthly Payment) (£)</label>
            <input
              id="tenantMonthly"
              type="text"
              bind:value={tenantMonthlyPayment}
              on:input={(e) => handleNumericInput(e, 'tenantMonthlyPayment')}
              placeholder="0.00"
            />
          </div>
          <div class="form-group">
            <label for="tenantWeekly">Tenant (Weekly Payment) (£)</label>
            <input
              id="tenantWeekly"
              type="text"
              bind:value={tenantWeeklyPayment}
              on:input={(e) => handleNumericInput(e, 'tenantWeeklyPayment')}
              placeholder="0.00"
            />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="apaHbMonthly">APA/HB (Monthly Payment) (£)</label>
            <input
              id="apaHbMonthly"
              type="text"
              bind:value={apaHbMonthlyPayment}
              on:input={(e) => handleNumericInput(e, 'apaHbMonthlyPayment')}
              placeholder="0.00"
            />
          </div>

          <div class="form-group">
            <label for="benefitsWeekly">Benefits (HB - Weekly Payment) (£)</label>
            <input
              id="benefitsWeekly"
              type="text"
              bind:value={benefitsHbWeeklyPayment}
              on:input={(e) => handleNumericInput(e, 'benefitsHbWeeklyPayment')}
              placeholder="0.00"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="tpdMonthly">TPD (Monthly Payment) (£)</label>
            <input
              id="tpdMonthly"
              type="text"
              bind:value={tpdMonthlyPayment}
              on:input={(e) => handleNumericInput(e, 'tpdMonthlyPayment')}
              placeholder="0.00"
            />
          </div>
        </div>
        
        <div class="form-row">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                bind:checked={scrfChecked}
                on:change={handleInputChange}
              />
              SCRF
            </label>
            
            <label class="checkbox-label">
              <input
                type="checkbox"
                bind:checked={apaTpdChecked}
                on:change={handleInputChange}
              />
              APA+TPD
            </label>
          </div>
        </div>
        
        <div class="form-row">
          <button class="btn-primary" on:click={handleUpdate}>
            Update
          </button>
        </div>
      </div>
      
      <!-- Output Section -->
      <div class="output-section">
        <h4>Calculations</h4>
        
        <div class="output-grid">
          <div class="output-item">
            <label>Monthly Rent</label>
            <span class="output-value">{formatCurrency(calculateMonthlyRent())}</span>
          </div>
          
          <div class="output-item">
            <label>Total Monthly Payments</label>
            <span class="output-value">{formatCurrency(calculateTotalMonthlyPayments())}</span>
          </div>
          
          <div class="output-item">
            <label>Total Weekly Payments</label>
            <span class="output-value">{formatCurrency(calculateTotalWeeklyPayments())}</span>
          </div>
          
          <div class="output-item">
            <label>Monthly Shortfall/Surplus</label>
            <span class="output-value" style="color: {getShortfallSurplusColor(calculateMonthlyShortfallSurplus())}">
              {formatCurrency(calculateMonthlyShortfallSurplus())}
            </span>
          </div>
          
          <div class="output-item">
            <label>Weekly Shortfall/Surplus</label>
            <span class="output-value" style="color: {getShortfallSurplusColor(calculateWeeklyShortfallSurplus())}">
              {formatCurrency(calculateWeeklyShortfallSurplus())}
            </span>
          </div>
          
          <div class="output-item">
            <label>Tenant Contribution (Monthly)</label>
            <span class="output-value">{formatCurrency(calculateTenantContributionMonthly())}</span>
          </div>
          
          <div class="output-item">
            <label>Tenant Contribution (Weekly)</label>
            <span class="output-value">{formatCurrency(calculateTenantContributionWeekly())}</span>
          </div>
          
          <div class="output-item">
            <label>Benefits Contribution (Monthly)</label>
            <span class="output-value">{formatCurrency(calculateBenefitsContributionMonthly())}</span>
          </div>
          
          <div class="output-item">
            <label>Benefits Contribution (Weekly)</label>
            <span class="output-value">{formatCurrency(calculateBenefitsContributionWeekly())}</span>
          </div>
          
          <div class="output-item">
            <label>Arrears will clear in</label>
            <span class="output-value">{calculateArrearsClearDate()}</span>
          </div>
          
          <div class="output-item">
            <label>4 weeks (arrears value)</label>
            <span class="output-value">{formatCurrency(calculate4WeeksArrears())}</span>
          </div>
          
          <div class="output-item">
            <label>8 weeks (arrears value)</label>
            <span class="output-value">{formatCurrency(calculate8WeeksArrears())}</span>
          </div>
          
          {#if currentBalance < 0}
            <div class="output-item">
              <label>Refund</label>
              <span class="output-value" style="color: #059669">{formatCurrency(calculateRefund())}</span>
            </div>
          {/if}
        </div>
        
        <div class="status-section">
          <div class="account-status">
            <strong>{getAccountStatus()}</strong>
          </div>
          
          <div class="escalation-suggestion">
            <label>Escalation suggestion:</label>
            <span>TBD by client's policy</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Notes Section -->
    <NotesSection tenantReference={reference} />
  </div>
</div>

<style>
  .rent-calculator {
    padding: 1.5rem;
  }

  .calculator-content h3 {
    margin: 0 0 1.5rem 0;
    color: #111827;
  }

  .calculator-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 1.5rem;
  }

  .input-section h4,
  .output-section h4 {
    margin: 0 0 1rem 0;
    color: #1f2937;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--color-primary);
  }

  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .input-with-button {
    display: flex;
    gap: 0.5rem;
  }

  .input-with-button input {
    flex: 1;
  }

  .copy-button {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    background-color: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 150ms ease-in-out;
  }

  .copy-button:hover:not(:disabled) {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }

  .copy-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .checkbox-group {
    display: flex;
    gap: 1.5rem;
    align-items: center;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
  }

  .checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
  }

  .status-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    border-left: 4px solid var(--color-primary);
  }

  .account-status {
    font-size: 1.125rem;
    color: #111827;
    margin-bottom: 0.5rem;
  }

  .escalation-suggestion {
    display: flex;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #4b5563;
  }

  .escalation-suggestion label {
    font-weight: 500;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .calculator-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    .rent-calculator {
      padding: 1rem;
    }

    .form-row {
      flex-direction: column;
      gap: 0.75rem;
    }

    .checkbox-group {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .input-with-button {
      flex-direction: column;
    }
  }
</style> 