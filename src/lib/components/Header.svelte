<script lang="ts">
  import { logout, currentUser } from '$lib/stores';
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher();
  
  // Menu handlers
  async function handleSave() {
    // TODO: Implement save functionality with tenant selection
    console.log('Save functionality - to be implemented with tenant management');
    alert('Save functionality will be implemented when tenant management is added.');
  }
  
  async function handleLoad() {
    // TODO: Implement load functionality with tenant selection
    console.log('Load functionality - to be implemented with tenant management');
    alert('Load functionality will be implemented when tenant management is added.');
  }
  
  function handleExit() {
    if (confirm('Are you sure you want to exit? Any unsaved changes will be lost.')) {
      logout();
    }
  }
  
  function handleSessionLog() {
    dispatch('sessionLog');
  }
  
  function handleOpenCaseLog() {
    dispatch('caseLog');
  }
  
  function handleUserGuide() {
    // TODO: Implement user guide
    console.log('User guide - to be implemented');
    alert('User guide will be implemented in a future update.');
  }
  
  function handleAbout() {
    alert(`Rent Collection Toolkit v0.1.0\n\nA modern web application for UK council and housing association rent collection agents.\n\nBuilt with SvelteKit and designed for efficiency and ease of use.`);
  }
</script>

<header class="app-header">
  <div class="header-content">
    <div class="header-left">
      <img src="/Rent-Collection-Toolkit-White-Logo.svg" alt="Rent Collection Toolkit Logo" class="app-logo">
    </div>
    
    <div class="header-center">
      <div class="council-logo">
        <div class="council-emblem">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 3L2 7V17C2 17.55 2.45 18 3 18H21C21.55 18 22 17.55 22 17V7L12 3ZM20 16H4V8.84L12 5.4L20 8.84V16ZM6 10H8V14H6V10ZM10 10H12V14H10V10ZM14 10H16V14H14V10ZM18 10H20V14H18V10Z"/>
          </svg>
        </div>
        <span class="council-name">Sample Council</span>
      </div>
    </div>
    
    <div class="header-right">
      {#if $currentUser}
        <div class="user-profile">
          <div class="user-avatar">
            {$currentUser.email.charAt(0).toUpperCase()}{$currentUser.email.split('@')[0].slice(-1).toUpperCase()}
          </div>
          <div class="user-dropdown">
            <div class="user-info">
              <span class="user-name">{$currentUser.email}</span>
              <span class="user-org">{$currentUser.organizationName}</span>
            </div>
            <hr class="dropdown-divider" />
            <button class="dropdown-item logout-btn" on:click={handleExit}>Logout</button>
          </div>
        </div>
      {/if}
    </div>
  </div>
</header>

<style>
  /* Header Styles */
  .app-header {
    background-color: var(--color-primary);
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
    color: #ffffff;
  }

  .app-logo {
    height: 50px;
    width: auto;
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .council-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .council-emblem {
    display: flex;
    align-items: center;
    color: #ffffff;
  }

  .council-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 1.125rem;
  }

  /* User Profile Styles */
  .user-profile {
    position: relative;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 150ms ease-in-out;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .user-avatar:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
  }

  .user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 150ms ease-in-out;
    z-index: 1000;
    margin-top: 0.5rem;
  }

  .user-profile:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .user-info {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .user-name {
    display: block;
    font-weight: 600;
    color: #111827;
    font-size: 0.875rem;
  }

  .user-org {
    display: block;
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    text-align: left;
    background: none;
    border: none;
    color: #374151;
    transition: background-color 150ms ease-in-out;
    font-size: 0.875rem;
  }

  .dropdown-item:hover {
    background-color: #f9fafb;
  }

  .logout-btn {
    color: #dc2626;
    font-weight: 500;
  }

  .logout-btn:hover {
    background-color: #fef2f2;
  }

  .dropdown-divider {
    margin: 0;
    border: none;
    border-top: 1px solid #e5e7eb;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .header-content {
      padding: 0.75rem 1rem;
    }

    .app-logo {
      height: 40px;
    }

    .council-name {
      font-size: 1rem;
    }

    .council-emblem {
      font-size: 1.25rem;
    }

    .user-avatar {
      width: 36px;
      height: 36px;
      font-size: 0.75rem;
    }
  }
</style> 