<script lang="ts">
  import { updateAccountChargesData, currentInstanceData } from '$lib/stores';
  import NotesSection from './NotesSection.svelte';
  
  // Local state for form inputs
  let grossWeeklyRent = 0;
  let rentComponent = 0;
  let nonUcServiceCharge1 = 0;
  let nonUcServiceCharge2 = 0;
  let nonUcServiceCharge3 = 0;
  
  // Synced data from Rent Calculator
  $: rentCalculatorData = $currentInstanceData?.rentCalculator;
  $: weeklyRentFromCalculator = rentCalculatorData?.weeklyRent || 0;
  $: reference = rentCalculatorData?.reference || '';
  
  // Subscribe to current instance data for account charges
  $: if ($currentInstanceData) {
    const data = $currentInstanceData.accountCharges;
    grossWeeklyRent = data.grossWeeklyRent || weeklyRentFromCalculator;
    rentComponent = data.rentComponent;
    nonUcServiceCharge1 = data.nonUcServiceCharge1;
    nonUcServiceCharge2 = data.nonUcServiceCharge2;
    nonUcServiceCharge3 = data.nonUcServiceCharge3;
  }
  
  // Auto-sync gross weekly rent when weekly rent changes in calculator
  $: if (weeklyRentFromCalculator !== grossWeeklyRent && weeklyRentFromCalculator > 0) {
    grossWeeklyRent = weeklyRentFromCalculator;
    handleInputChange();
  }
  
  // Calculation functions
  
  // Calculation 18: Gross Monthly Rent
  function calculateGrossMonthlyRent(): number {
    return grossWeeklyRent * 52 / 12;
  }
  
  // Calculation 19: Total non-eligible (service charges)
  function calculateTotalNonEligible(): number {
    return nonUcServiceCharge1 + nonUcServiceCharge2 + nonUcServiceCharge3;
  }
  
  // Calculation 20: UC service charges (Eligible service charges) - Weekly
  function calculateEligibleServiceChargesWeekly(): number {
    return grossWeeklyRent - rentComponent - calculateTotalNonEligible();
  }
  
  // Calculation 21: Full entitlement (HB) - Weekly
  function calculateHbFullEntitlementWeekly(): number {
    return rentComponent + calculateEligibleServiceChargesWeekly();
  }
  
  // Calculation 22: Full entitlement (UC) - Monthly
  function calculateUcFullEntitlementMonthly(): number {
    return (rentComponent + calculateEligibleServiceChargesWeekly()) * 52 / 12;
  }
  
  // Calculation 23: Low under occupation (UC Entitlement) - Monthly
  function calculateUcLowUnderOccupationMonthly(): number {
    return calculateUcFullEntitlementMonthly() * 0.86;
  }
  
  // Calculation 24: High under occupation (UC Entitlement) - Monthly
  function calculateUcHighUnderOccupationMonthly(): number {
    return calculateUcFullEntitlementMonthly() * 0.75;
  }
  
  // Calculation 25: HB Shortfall - Weekly
  function calculateHbShortfallWeekly(): number {
    return grossWeeklyRent - calculateHbFullEntitlementWeekly();
  }
  
  // Calculation 26: UC Full Entitlement Shortfall - Monthly
  function calculateUcFullEntitlementShortfallMonthly(): number {
    return calculateGrossMonthlyRent() - calculateUcFullEntitlementMonthly();
  }
  
  // Calculation 27: UC Low Under Occupation Shortfall - Monthly
  function calculateUcLowUnderOccupationShortfallMonthly(): number {
    return calculateGrossMonthlyRent() - calculateUcLowUnderOccupationMonthly();
  }
  
  // Calculation 28: UC High Under Occupation Shortfall - Monthly
  function calculateUcHighUnderOccupationShortfallMonthly(): number {
    return calculateGrossMonthlyRent() - calculateUcHighUnderOccupationMonthly();
  }
  
  function formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(value);
  }
  
  function sanitizeNumericInput(value: string): number {
    const cleaned = value.trim().replace(/,/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
  
  function handleInputChange() {
    updateAccountChargesData({
      grossWeeklyRent,
      rentComponent,
      nonUcServiceCharge1,
      nonUcServiceCharge2,
      nonUcServiceCharge3
    });
  }
  
  function handleNumericInput(event: Event, field: string) {
    const target = event.target as HTMLInputElement;
    const sanitizedValue = sanitizeNumericInput(target.value);
    
    switch(field) {
      case 'grossWeeklyRent':
        grossWeeklyRent = sanitizedValue;
        break;
      case 'rentComponent':
        rentComponent = sanitizedValue;
        break;
      case 'nonUcServiceCharge1':
        nonUcServiceCharge1 = sanitizedValue;
        break;
      case 'nonUcServiceCharge2':
        nonUcServiceCharge2 = sanitizedValue;
        break;
      case 'nonUcServiceCharge3':
        nonUcServiceCharge3 = sanitizedValue;
        break;
    }
    
    handleInputChange();
  }
  
  function handleUpdate() {
    // Trigger all calculations (they're reactive, so this just forces an update)
    handleInputChange();
  }
  
  function getShortfallColor(value: number): string {
    if (value > 0) return '#dc2626'; // Shortfall is bad
    if (value < 0) return '#059669'; // Surplus is good
    return '#111827';
  }
</script>

<div class="account-charges">
  <div class="charges-content">
    <h3>Account Charges</h3>
    
    <!-- Data Synchronization Display -->
    <div class="sync-data">
      <h4>Synchronized Data from Rent Calculator</h4>
      <div class="sync-grid">
        <div class="sync-item">
          <label>Weekly Rent (from Calculator):</label>
          <span class="value">{formatCurrency(weeklyRentFromCalculator)}</span>
        </div>
      </div>
    </div>
    
    <!-- Input Fields -->
    <div class="input-section">
      <h4>Rent and Service Charges</h4>
      <div class="input-grid">
        <div class="input-group">
          <label for="grossWeeklyRent">Gross Weekly Rent:</label>
          <input
            id="grossWeeklyRent"
            type="number"
            min="0"
            step="0.01"
            value={grossWeeklyRent}
            on:input={(e) => handleNumericInput(e, 'grossWeeklyRent')}
          />
          <small class="input-help">Auto-synced from Rent Calculator, but can be overridden</small>
        </div>
        
        <div class="input-group">
          <label for="rentComponent">Rent (component of Gross Weekly):</label>
          <input
            id="rentComponent"
            type="number"
            min="0"
            step="0.01"
            value={rentComponent}
            on:input={(e) => handleNumericInput(e, 'rentComponent')}
          />
        </div>
        
        <div class="input-group">
          <label for="nonUcServiceCharge1">Non-UC service charge 1:</label>
          <input
            id="nonUcServiceCharge1"
            type="number"
            min="0"
            step="0.01"
            value={nonUcServiceCharge1}
            on:input={(e) => handleNumericInput(e, 'nonUcServiceCharge1')}
          />
        </div>
        
        <div class="input-group">
          <label for="nonUcServiceCharge2">Non-UC service charge 2:</label>
          <input
            id="nonUcServiceCharge2"
            type="number"
            min="0"
            step="0.01"
            value={nonUcServiceCharge2}
            on:input={(e) => handleNumericInput(e, 'nonUcServiceCharge2')}
          />
        </div>
        
        <div class="input-group">
          <label for="nonUcServiceCharge3">Non-UC service charge 3:</label>
          <input
            id="nonUcServiceCharge3"
            type="number"
            min="0"
            step="0.01"
            value={nonUcServiceCharge3}
            on:input={(e) => handleNumericInput(e, 'nonUcServiceCharge3')}
          />
        </div>
      </div>
      
      <div class="button-section">
        <button class="btn-primary" on:click={handleUpdate}>
          Update
        </button>
      </div>
    </div>
    
    <!-- Calculations Display -->
    <div class="calculations-section">
      <h4>Calculations</h4>
      <div class="calculations-grid">
        <!-- Basic Calculations -->
        <div class="calc-group">
          <h5>Basic Calculations</h5>
          <div class="calc-item">
            <label>Gross Monthly Rent:</label>
            <span class="value">{formatCurrency(calculateGrossMonthlyRent())}</span>
          </div>
          <div class="calc-item">
            <label>Total non-eligible (service charges):</label>
            <span class="value">{formatCurrency(calculateTotalNonEligible())}</span>
          </div>
          <div class="calc-item">
            <label>UC service charges (Eligible service charges) - Weekly:</label>
            <span class="value">{formatCurrency(calculateEligibleServiceChargesWeekly())}</span>
          </div>
        </div>
        
        <!-- Entitlement Column -->
        <div class="calc-group">
          <h5>Entitlements</h5>
          <div class="calc-item">
            <label>Full entitlement (HB) - Weekly:</label>
            <span class="value">{formatCurrency(calculateHbFullEntitlementWeekly())}</span>
          </div>
          <div class="calc-item">
            <label>Full entitlement (UC) - Monthly:</label>
            <span class="value">{formatCurrency(calculateUcFullEntitlementMonthly())}</span>
          </div>
          <div class="calc-item">
            <label>Low under occupation (UC Entitlement) - Monthly:</label>
            <span class="value">{formatCurrency(calculateUcLowUnderOccupationMonthly())}</span>
          </div>
          <div class="calc-item">
            <label>High under occupation (UC Entitlement) - Monthly:</label>
            <span class="value">{formatCurrency(calculateUcHighUnderOccupationMonthly())}</span>
          </div>
        </div>
        
        <!-- Shortfall Column -->
        <div class="calc-group">
          <h5>Shortfalls</h5>
          <div class="calc-item">
            <label>HB Shortfall - Weekly:</label>
            <span class="value" style="color: {getShortfallColor(calculateHbShortfallWeekly())}">{formatCurrency(calculateHbShortfallWeekly())}</span>
          </div>
          <div class="calc-item">
            <label>UC Full Entitlement Shortfall - Monthly:</label>
            <span class="value" style="color: {getShortfallColor(calculateUcFullEntitlementShortfallMonthly())}">{formatCurrency(calculateUcFullEntitlementShortfallMonthly())}</span>
          </div>
          <div class="calc-item">
            <label>UC Low Under Occupation Shortfall - Monthly:</label>
            <span class="value" style="color: {getShortfallColor(calculateUcLowUnderOccupationShortfallMonthly())}">{formatCurrency(calculateUcLowUnderOccupationShortfallMonthly())}</span>
          </div>
          <div class="calc-item">
            <label>UC High Under Occupation Shortfall - Monthly:</label>
            <span class="value" style="color: {getShortfallColor(calculateUcHighUnderOccupationShortfallMonthly())}">{formatCurrency(calculateUcHighUnderOccupationShortfallMonthly())}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Notes Section -->
    <NotesSection tenantReference={reference} />
  </div>
</div>

<style>
  .account-charges {
    padding: 1.5rem;
  }

  .charges-content h3 {
    margin: 0 0 1.5rem 0;
    color: #111827;
  }

  .charges-content h4 {
    margin: 1.5rem 0 1rem 0;
    color: #1f2937;
    font-size: 1.1rem;
  }

  .charges-content h5 {
    margin: 0 0 0.75rem 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
  }

  /* Synchronized Data Section */
  .sync-data {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .sync-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
  }

  .sync-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: white;
    border-radius: 0.125rem;
  }

  .sync-item label {
    font-weight: 500;
    color: #374151;
  }

  .sync-item .value {
    font-weight: 600;
    color: #111827;
  }

  /* Input Section */
  .input-section {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .input-group label {
    font-weight: 500;
    color: #374151;
  }

  .input-group input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.125rem;
    font-size: 1rem;
  }

  .input-group input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .input-help {
    color: #6b7280;
    font-size: 0.875rem;
    font-style: italic;
  }

  .button-section {
    display: flex;
    gap: 0.75rem;
  }

  /* Calculations Section */
  .calculations-section {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .calculations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .calc-group {
    background-color: white;
    border-radius: 0.375rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
  }

  .calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
  }

  .calc-item:last-child {
    border-bottom: none;
  }

  .calc-item label {
    font-weight: 500;
    color: #374151;
    flex: 1;
  }

  .calc-item .value {
    font-weight: 600;
    color: #111827;
    text-align: right;
    margin-left: 0.75rem;
  }

  /* Utility classes */
  .text-success {
    color: var(--color-green-600);
  }

  .text-error {
    color: var(--color-red-600);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .account-charges {
      padding: 1rem;
    }

    .sync-grid,
    .input-grid,
    .calculations-grid {
      grid-template-columns: 1fr;
    }

    .sync-item,
    .calc-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }

    .calc-item .value {
      margin-left: 0;
      text-align: left;
    }
  }
</style> 