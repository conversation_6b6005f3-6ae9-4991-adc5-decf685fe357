<script lang="ts">
  import { activeInstance, currentInstanceData, isAuthenticated, currentUser, clearAllData, clearCurrentInstance } from '$lib/stores';
  import type { InstanceType } from '$lib/stores';
  import RentCalculator from '$lib/components/RentCalculator.svelte';
  import ArrangementPlanner from '$lib/components/ArrangementPlanner.svelte';
  import AccountCharges from '$lib/components/AccountCharges.svelte';
  import LoginForm from '$lib/components/LoginForm.svelte';
  
  type TabType = 'rent-calculator' | 'arrangement-planner' | 'account-charges';
  
  let activeTab: TabType = 'rent-calculator';
  
  function setActiveTab(tab: TabType) {
    activeTab = tab;
  }
  
  function setActiveInstance(instance: InstanceType) {
    activeInstance.set(instance);
  }
  
  function getInstanceDisplayName(instance: InstanceType): string {
    return instance === 'inbound' ? 'Inbound' : 'Outbound';
  }
  
  function getInstanceClass(instance: InstanceType): string {
    return instance === 'inbound' ? 'instance-inbound' : 'instance-outbound';
  }
  
  function handleClearAll() {
    if (confirm('Are you sure you want to clear all data in both Inbound and Outbound instances? This action cannot be undone.')) {
      clearAllData();
    }
  }
  
  function handleClearCurrent() {
    const instanceName = getInstanceDisplayName($activeInstance);
    if (confirm(`Are you sure you want to clear all data in the ${instanceName} instance? This action cannot be undone.`)) {
      clearCurrentInstance();
    }
  }

  function handleCaseLog() {
    // This will be handled by the parent layout component
    const event = new CustomEvent('openCaseLog');
    window.dispatchEvent(event);
  }
</script>

{#if !$isAuthenticated}
  <LoginForm />
{:else}
  <div class="dashboard">
    <!-- Instance Selector -->
    <div class="instance-selector">
      <div class="instance-content">
        <div class="instance-buttons">
          {#each (['inbound', 'outbound'] as InstanceType[]) as instance}
            <button
              class="instance-button {getInstanceClass(instance)} {$activeInstance === instance ? 'active' : ''}"
              on:click={() => setActiveInstance(instance)}
            >
              <div class="instance-indicator"></div>
              <span>{getInstanceDisplayName(instance)}</span>
            </button>
          {/each}
          <button class="case-log-btn" on:click={handleCaseLog}>
            Case Log
          </button>
        </div>
        <div class="instance-actions">
          <button class="action-button clear-current" on:click={handleClearCurrent}>
            Clear Current
          </button>
          <button class="action-button clear-all" on:click={handleClearAll}>
            Clear All
          </button>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <div class="tab-buttons">
        <button
          class="tab-button {activeTab === 'rent-calculator' ? 'active' : ''}"
          on:click={() => setActiveTab('rent-calculator')}
        >
          Rent Calculator
        </button>
        <button
          class="tab-button {activeTab === 'arrangement-planner' ? 'active' : ''}"
          on:click={() => setActiveTab('arrangement-planner')}
        >
          Arrangement Planner
        </button>
        <button
          class="tab-button {activeTab === 'account-charges' ? 'active' : ''}"
          on:click={() => setActiveTab('account-charges')}
        >
          Account Charges
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content {getInstanceClass($activeInstance)}">
      {#if activeTab === 'rent-calculator'}
        <RentCalculator />
      {:else if activeTab === 'arrangement-planner'}
        <ArrangementPlanner />
      {:else if activeTab === 'account-charges'}
        <AccountCharges />
      {/if}
    </div>
  </div>
{/if}

<style>
  .dashboard {
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Instance Selector */
  .instance-selector {
    background-color: #ffffff;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    margin-bottom: 1.5rem;
  }

  .instance-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }

  .instance-actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 150ms ease-in-out;
    background: transparent;
  }

  .action-button.clear-current {
    color: var(--color-primary);
    border-color: var(--color-primary);
  }

  .action-button.clear-current:hover:not(:disabled) {
    background-color: var(--color-primary);
    color: #ffffff;
  }

  .action-button.clear-all {
    color: var(--color-red);
    border-color: var(--color-red);
  }

  .action-button.clear-all:hover:not(:disabled) {
    background-color: var(--color-red);
    color: #ffffff;
  }

  .action-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .instance-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .instance-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid transparent;
    border-radius: 0.375rem;
    background-color: #f9fafb;
    color: #374151;
    font-weight: 500;
    transition: all 200ms ease-in-out;
    cursor: pointer;
    min-width: 100px;
    font-size: 0.875rem;
  }

  .instance-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .instance-button.active {
    border-color: currentColor;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    transform: translateY(-2px);
  }

  .instance-button.instance-inbound {
    background-color: rgba(2, 85, 130, 0.1);
    color: var(--color-primary);
    border-color: rgba(2, 85, 130, 0.2);
  }

  .instance-button.instance-outbound {
    background-color: rgba(0, 51, 68, 0.1);
    color: var(--color-secondary);
    border-color: rgba(0, 51, 68, 0.2);
  }

  .case-log-btn {
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background-color: #ffffff;
    color: #374151;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 150ms ease-in-out;
  }

  .case-log-btn:hover {
    background-color: #f9fafb;
    border-color: var(--color-primary);
    color: var(--color-primary);
  }

  .instance-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: currentColor;
  }

  /* Tab Navigation */
  .tab-navigation {
    margin-bottom: 0;
    position: relative;
  }

  /* Tab Content */
  .tab-content {
    background-color: #ffffff;
    border-radius: 0 0 0.5rem 0.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    min-height: 600px;
    margin-top: -1px;
    position: relative;
    z-index: 0;
  }

  .tab-content.instance-inbound {
    border-left: 4px solid var(--color-primary);
  }

  .tab-content.instance-outbound {
    border-left: 4px solid var(--color-secondary);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .instance-content {
      flex-direction: column;
      gap: 1rem;
    }

    .instance-buttons {
      flex-wrap: wrap;
      justify-content: center;
    }

    .instance-button {
      min-width: auto;
      flex: 1;
    }

    .case-log-btn {
      flex: 1;
    }

    .tab-button {
      text-align: center;
      padding: 0.75rem 1rem;
    }

    .instance-selector {
      padding: 1rem;
    }

    .instance-actions {
      width: 100%;
      justify-content: center;
    }
  }
</style>
