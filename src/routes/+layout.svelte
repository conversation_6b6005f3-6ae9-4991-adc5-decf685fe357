<script lang="ts">
  import '../app.css';
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { logout, saveInstanceData, loadInstanceData, currentUser, clearAllData } from '$lib/stores';
  import SessionLog from '$lib/components/SessionLog.svelte';
  import CaseLog from '$lib/components/CaseLog.svelte';
  import Header from '$lib/components/Header.svelte';
  
  // Global state for session tracking
  let sessionStartTime = new Date();
  let showSessionLog = false;
  let showCaseLog = false;
  
  onMount(() => {
    sessionStartTime = new Date();
    
    // Listen for case log events from child components
    window.addEventListener('openCaseLog', () => {
      showCaseLog = true;
    });
  });
  
  function formatSessionTime(date: Date): string {
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  // Event handlers for header events
  function handleSessionLog() {
    showSessionLog = true;
  }
  
  function handleCaseLog() {
    showCaseLog = true;
  }

  // Footer menu handlers
  async function handleSave() {
    console.log('Save functionality - to be implemented with tenant management');
    alert('Save functionality will be implemented when tenant management is added.');
  }
  
  async function handleLoad() {
    console.log('Load functionality - to be implemented with tenant management');
    alert('Load functionality will be implemented when tenant management is added.');
  }
  
  function handleUserGuide() {
    console.log('User guide - to be implemented');
    alert('User guide will be implemented in a future update.');
  }
  
  function handleAbout() {
    alert(`Rent Collection Toolkit v0.1.0\n\nA modern web application for UK council and housing association rent collection agents.\n\nBuilt with SvelteKit and designed for efficiency and ease of use.`);
  }
</script>

<div class="app-container">
  <!-- Header -->
  <Header on:sessionLog={handleSessionLog} on:caseLog={handleCaseLog} />

  <!-- Main Content Area -->
  <main class="app-main">
    <slot />
  </main>

  <!-- Footer -->
  <footer class="app-footer">
    <div class="footer-content">
      <div class="footer-left">
        <nav class="footer-nav">
          <div class="nav-menu">
            <span class="nav-label">File</span>
            <div class="nav-items">
              <button class="nav-item" on:click={handleSave}>Save</button>
              <button class="nav-item" on:click={handleLoad}>Load</button>
            </div>
          </div>
          
          <div class="nav-menu">
            <span class="nav-label">View</span>
            <div class="nav-items">
              <button class="nav-item" on:click={handleSessionLog}>Session Log</button>
              <button class="nav-item" on:click={handleCaseLog}>Case Log</button>
            </div>
          </div>
          
          <div class="nav-menu">
            <span class="nav-label">Help</span>
            <div class="nav-items">
              <button class="nav-item" on:click={handleUserGuide}>User Guide</button>
              <button class="nav-item" on:click={handleAbout}>About</button>
            </div>
          </div>
        </nav>
      </div>
      
      <div class="footer-center">
        <span class="session-info">
          Session: {formatSessionTime(sessionStartTime)}
        </span>
      </div>
      
      <div class="footer-right">
        <span class="version-info">v0.1.0</span>
      </div>
    </div>
  </footer>
</div>

<!-- Session Log Modal -->
<SessionLog bind:isOpen={showSessionLog} />

<!-- Case Log Modal -->
<CaseLog bind:isOpen={showCaseLog} />

<style lang="less">
  :global {
    :root {
      --color-primary: #025582;
      --color-secondary: rgb(0, 51, 68);
      --color-white: #ffffff;
      --color-black: #071821;
      --color-red: #dc2626;
      --bg: #0255822d;
    }
    .output-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
      .output-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;
        padding: 0.75rem;
        background-color: #f9fafb;
        border-radius: 0.375rem;
        border: 3px solid #d1d5db;
        label {
          font-weight: 500;
          color: #374151;
          font-size: 0.875rem;
        }
        .output-value {
          font-weight: 600;
          color: #111827;
        }
      }
    }
    .tab-navigation {
    margin-bottom: 0;
    position: relative;
  }

  .tab-buttons {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.5rem;
    gap: 0.25rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    .tab-button {
      flex: 1;
      padding: 0.75rem 1.5rem;
      background-color: transparent;
      border: none;
      border-radius: 0.375rem;
      color: #4b5563;
      font-weight: 500;
      font-size: 0.875rem;
      transition: all 200ms ease-in-out;
      cursor: pointer;
      position: relative;
      z-index: 1;
      &:hover {
        background-color: #ffffff;
        color: #1f2937;
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        transform: translateY(-1px);
      }
      &.active {
        background-color: #ffffff;
        color: var(--color-primary);
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        transform: translateY(-2px);
        font-weight: 600;
        &:after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #ffffff;
          z-index: 2;
        }
      }
    }
  }

    @media (max-width: 768px) {
      .output-grid {
        grid-template-columns: 1fr 1fr;
        .sync-item,
        .output-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;
        }
      }

      .tab-buttons {
        gap: 0.25rem;
      }

    }

  }


  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f9fafb;
  }

  /* Main Content */
  .app-main {
    background: var(--bg);
    flex: 1;
    padding: 1.5rem;
    margin: 0 auto;
    width: 100%;
  }

  /* Footer Styles */
  .app-footer {
    background-color: #ffffff;
    border-top: 1px solid #e5e7eb;
    padding: 0.75rem 1.5rem;
  }

  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    font-size: 0.875rem;
    color: #4b5563;
  }

  .footer-nav {
    display: flex;
    gap: 2rem;
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .nav-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .nav-items {
    display: flex;
    gap: 0.5rem;
  }

  .nav-item {
    background: none;
    border: none;
    padding: 0.25rem 0.5rem;
    color: #6b7280;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 150ms ease-in-out;
  }

  .nav-item:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .footer-center {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .session-info {
    font-weight: 500;
    color: #6b7280;
    font-size: 0.75rem;
  }

  .version-info {
    color: #6b7280;
    font-size: 0.75rem;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .app-main {
      padding: 1rem;
    }

    .footer-content {
      flex-direction: column;
      gap: 0.75rem;
      text-align: center;
    }

    .footer-nav {
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }

    .nav-menu {
      flex-direction: column;
      gap: 0.25rem;
      align-items: center;
    }

    .nav-items {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
</style> 